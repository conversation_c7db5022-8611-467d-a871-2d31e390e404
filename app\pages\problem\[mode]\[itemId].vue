<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <UContainer class="py-8">
      <!-- Header -->
      <div class="flex items-center justify-between mb-8">
        <div class="flex items-center space-x-4">
          <UButton
            @click="navigateBack"
            variant="ghost"
            icon="i-lucide-arrow-left"
            size="lg"
          >
            Back to Game
          </UButton>
          <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ getPageTitle() }}
            </h1>
            <p class="text-gray-600 dark:text-gray-300">
              Problem {{ route.params.itemId }} of 37
            </p>
          </div>
        </div>

        <!-- Progress -->
        <div class="text-right">
          <div class="text-lg font-semibold text-blue-600 dark:text-blue-400">
            Item {{ route.params.itemId }}
          </div>
          <div class="text-sm text-gray-500 dark:text-gray-400">
            {{ gameStore.state.totalCompleted }} / 37 completed
          </div>
        </div>
      </div>

      <!-- Problem Content -->
      <div v-if="currentProblem" class="max-w-4xl mx-auto">
        <UCard class="mb-6">
          <template #header>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
              {{ getProblemTitle() }}
            </h2>
          </template>

          <div class="space-y-6">
            <!-- Problem Statement -->
            <div class="space-y-2">
              <div class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Problem:
              </div>
              <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                <div class="text-blue-900 dark:text-blue-100 font-mono text-lg">
                  {{ getProblemStatement() }}
                </div>
              </div>
            </div>

            <!-- Function Display for Domain/Range Problems -->
            <div v-if="currentProblem.type === 'domain-range'" class="space-y-2">
              <div class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Function:
              </div>
              <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
                <div class="text-green-900 dark:text-green-100 font-mono text-xl text-center">
                  f(x) = {{ (currentProblem as DomainRangeProblem).equation }}
                </div>
              </div>
            </div>

            <!-- Answer Input -->
            <div class="space-y-2">
              <label class="text-lg font-medium text-gray-900 dark:text-white">
                Your Answer:
              </label>
              <UTextarea
                v-model="currentAnswer"
                placeholder="Enter your answer here..."
                :rows="3"
                size="lg"
                class="font-mono"
              />
            </div>

            <!-- Math Keyboard -->
            <div class="space-y-2">
              <div class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Math Symbols:
              </div>
              <MathKeyboard v-model="currentAnswer" />
            </div>

            <!-- Hint (if available) -->
            <div v-if="currentProblem.hint" class="space-y-2">
              <UAlert
                icon="i-lucide-lightbulb"
                color="warning"
                variant="soft"
                :title="'Hint'"
                :description="currentProblem.hint"
              />
            </div>

            <!-- Validation Feedback -->
            <div v-if="validationMessage" class="space-y-2">
              <UAlert
                :icon="isCorrect ? 'i-lucide-check-circle' : 'i-lucide-x-circle'"
                :color="isCorrect ? 'success' : 'error'"
                variant="soft"
                :title="isCorrect ? 'Correct!' : 'Try Again'"
                :description="validationMessage"
                role="alert"
              />
            </div>
          </div>

          <template #footer>
            <div class="flex justify-between items-center">
              <UButton @click="navigateBack" variant="outline">
                Cancel
              </UButton>
              <UButton
                @click="submitAnswer"
                :disabled="!currentAnswer.trim()"
                :loading="submitting"
                size="lg"
              >
                Submit Answer
              </UButton>
            </div>
          </template>
        </UCard>
      </div>

      <!-- Loading State -->
      <div v-else class="flex justify-center items-center min-h-[400px]">
        <div class="text-center">
          <Icon name="i-lucide-loader-2" class="w-8 h-8 animate-spin mx-auto mb-4" />
          <p class="text-gray-600 dark:text-gray-300">Loading problem...</p>
        </div>
      </div>
    </UContainer>
  </div>
</template>

<script setup lang="ts">
import type { Problem, DomainRangeProblem, NotationProblem, GameMode } from '~/types/game'
import { validateAnswer } from '~/utils/mathValidation'
import { generateNotationProblem, generateDomainRangeProblem } from '~/utils/problemGenerator'

// Route and navigation
const route = useRoute()
const router = useRouter()

// Store
const gameStore = useGameStore()

// State
const currentProblem = ref<Problem | null>(null)
const currentAnswer = ref('')
const submitting = ref(false)
const validationMessage = ref('')
const isCorrect = ref(false)

// Computed
const mode = computed(() => route.params.mode as GameMode)
const itemId = computed(() => parseInt(route.params.itemId as string))

// SEO Meta
useSeoMeta({
  title: computed(() => `Problem ${itemId.value} - ${getPageTitle()}`),
  description: computed(() => `Solve ${mode.value === 'notation-conversion' ? 'notation conversion' : 'domain and range'} problem ${itemId.value}`),
})

// Methods
const getPageTitle = () => {
  if (mode.value === 'notation-conversion') {
    return 'Notation Conversion Problem'
  } else {
    return 'Domain & Range Problem'
  }
}

const getProblemTitle = () => {
  if (!currentProblem.value) return ''
  
  if (currentProblem.value.type === 'notation-conversion') {
    const notationProblem = currentProblem.value as NotationProblem
    return `Convert to ${notationProblem.targetNotation.replace('-', ' ')} notation`
  } else {
    const drProblem = currentProblem.value as DomainRangeProblem
    return `Find the ${drProblem.questionType === 'domain' ? 'Domain' : 'Range'}`
  }
}

const getProblemStatement = () => {
  if (!currentProblem.value) return ''
  
  if (currentProblem.value.type === 'notation-conversion') {
    const notationProblem = currentProblem.value as NotationProblem
    return notationProblem.problem
  } else {
    const drProblem = currentProblem.value as DomainRangeProblem
    return `Find the ${drProblem.questionType} of the function below:`
  }
}

const navigateBack = () => {
  router.push(`/${mode.value}`)
}

const submitAnswer = async () => {
  if (!currentAnswer.value.trim() || !currentProblem.value) return

  submitting.value = true

  try {
    const validation = validateAnswer(currentAnswer.value, currentProblem.value)

    validationMessage.value = validation.message
    isCorrect.value = validation.isCorrect

    // Update game store
    gameStore.submitAnswer(validation.isCorrect, itemId.value)

    if (validation.isCorrect) {
      // Wait a moment to show success message, then navigate back
      setTimeout(() => {
        navigateBack()
      }, 2000)
    } else {
      // Generate new problem for incorrect answer
      generateNewProblem()
      currentAnswer.value = ''
    }
  } catch (error) {
    validationMessage.value = 'An error occurred while validating your answer.'
    isCorrect.value = false
  } finally {
    submitting.value = false
  }
}

const generateNewProblem = () => {
  if (mode.value === 'notation-conversion') {
    currentProblem.value = generateNotationConversionProblem()
  } else if (mode.value === 'domain-range') {
    currentProblem.value = generateDomainRangeProblem('linear', 'domain')
  }
}

const generateNotationConversionProblem = (): Problem => {
  const conversions = [
    { source: 'algebraic' as const, target: 'interval' as const },
    { source: 'algebraic' as const, target: 'set-builder' as const },
    { source: 'interval' as const, target: 'algebraic' as const },
    { source: 'interval' as const, target: 'set-builder' as const },
    { source: 'set-builder' as const, target: 'algebraic' as const },
    { source: 'set-builder' as const, target: 'interval' as const }
  ]

  const conversion = conversions[Math.floor(Math.random() * conversions.length)]
  return generateNotationProblem(conversion.source, conversion.target, { includeHints: true })
}

// Initialize problem on mount
onMounted(() => {
  // Ensure game is initialized
  if (!gameStore.isGameActive) {
    gameStore.initializeGame(mode.value)
  }

  // Check if item is already completed
  if (gameStore.state.items[itemId.value]?.completed) {
    navigateBack()
    return
  }

  // Generate problem
  generateNewProblem()
})

// Clear validation when user types
watch(currentAnswer, () => {
  if (validationMessage.value) {
    validationMessage.value = ''
    isCorrect.value = false
  }
})
</script>
